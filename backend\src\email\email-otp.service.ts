import { Injectable, UnauthorizedException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { z } from "zod";
import { getError } from "src/common/errors";
import { Prisma } from "@prisma/client";
import { PrismaService } from "src/prisma/prisma.service";

type CheckEmailOtpDto = {
    email: string;
    otp: string;
};

type CreateEmailOtpDto = {
    email: string;
    otp: string;
    ipAddress: string | null;
    userAgent: string | null;
};

type DeleteEmailOtpDto = {
    id: string;
};

@Injectable()
export class EmailOtpService {
    constructor(
        private readonly configService: ConfigService,
        private readonly prisma: PrismaService,
    ) {}

    async create(createUserOtpDto: CreateEmailOtpDto) {
        const expirationTime = z.coerce
            .number()
            .int()
            .positive()
            .parse(this.configService.get("OTP_EXPIRATION_TIME_MS"));

        const otp = await this.prisma.userOtp.create({
            data: {
                email: createUserOtpDto.email,
                otp: createUserOtpDto.otp,
                ipAddress: createUserOtpDto.ipAddress,
                userAgent: createUserOtpDto.userAgent,
                expiresAt: new Date(Date.now() + expirationTime),
            },
        });

        return otp;
    }

    async check(checkUserOtpDto: CheckEmailOtpDto) {
        const where = {
            email: checkUserOtpDto.email,
            otp: checkUserOtpDto.otp,
            expiresAt: {
                gt: new Date(),
            },
            deletedAt: null,
        } satisfies Prisma.UserOtpWhereInput;

        const otp = await this.prisma.userOtp.findFirst({
            where: where,
        });

        if (!otp) {
            throw new UnauthorizedException(...getError("otp_invalid"));
        }

        return otp;
    }

    async softDelete(deleteUserOtpDto: DeleteEmailOtpDto) {
        await this.prisma.userOtp.update({
            where: {
                id: deleteUserOtpDto.id,
            },
            data: {
                deletedAt: new Date(),
            },
        });
    }

    async delete(deleteUserOtpDto: DeleteEmailOtpDto) {
        await this.prisma.userOtp.delete({
            where: {
                id: deleteUserOtpDto.id,
            },
        });
    }
}
