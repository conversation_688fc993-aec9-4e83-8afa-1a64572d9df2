<script lang="ts">
  import type { Common } from "@commune/api";
  import { goto } from "$app/navigation";
  import { getClient } from "$lib/acrpc";
  import {
    Modal,
    LocalizedInput,
    LocalizedEditor,
    TagPicker,
    ReactorHubPicker,
    ReactorCommunityPicker,
  } from "$lib/components";

  interface Props {
    show: boolean;
    locale: Common.WebsiteLocale;
    toLocaleHref: (href: string) => string;
    onClose: () => void;
    onPostCreated?: () => void;
  }

  const { show, locale, toLocaleHref, onClose, onPostCreated }: Props = $props();

  const { fetcher: api } = getClient();

  // i18n translations
  const i18n = {
    en: {
      createPostTitle: "Create New Post",
      cancel: "Cancel",
      create: "Create",
      hub: "Hub",
      hubPlaceholder: "Select hub (optional)...",
      community: "Community",
      communityPlaceholder: "Select community (optional)...",
      hubDisabledByCommunity: "Hub selection is disabled when Community is specified",
      communityDisabledByHub: "Community selection is disabled when Hub is specified",
      tags: "Tags",
      title: "Title",
      titlePlaceholder: "Enter post title...",
      body: "Body",
      bodyPlaceholder: "Write your post content...",
      titleRequired: "Title is required",
      bodyRequired: "Body is required",
      createSuccess: "Post created successfully!",
      createError: "Failed to create post",
    },
    ru: {
      createPostTitle: "Создать новый пост",
      cancel: "Отмена",
      create: "Создать",
      hub: "Хаб",
      hubPlaceholder: "Выберите хаб (необязательно)...",
      community: "Сообщество",
      communityPlaceholder: "Выберите сообщество (необязательно)...",
      hubDisabledByCommunity: "Выбор хаба отключен, когда указано сообщество",
      communityDisabledByHub: "Выбор сообщества отключен, когда указан хаб",
      tags: "Теги",
      title: "Заголовок",
      titlePlaceholder: "Введите заголовок поста...",
      body: "Содержание",
      bodyPlaceholder: "Напишите содержание поста...",
      titleRequired: "Заголовок обязателен",
      bodyRequired: "Содержание обязательно",
      createSuccess: "Пост успешно создан!",
      createError: "Не удалось создать пост",
    },
  };

  const t = $derived(i18n[locale]);

  // Form state
  let postTitle = $state<Common.Localizations>([]);
  let postBody = $state<Common.Localizations>([]);
  let hubId = $state<string | null>(null);
  let communityId = $state<string | null>(null);
  let selectedTags = $state<string[]>([]);
  let isSubmitting = $state(false);
  let formError = $state<string | null>(null);
  let formSuccess = $state<string | null>(null);

  // Handle mutual exclusivity between hub and community selection
  $effect(() => {
    // When a hub is selected, clear the community
    if (hubId) {
      communityId = null;
    }

    // When a community is selected, clear the hub
    if (communityId) {
      hubId = null;
    }
  });

  // Reset form when modal is opened
  $effect(() => {
    if (show) {
      resetForm();
    }
  });

  function resetForm() {
    postTitle = [];
    postBody = [];
    hubId = null;
    communityId = null;
    selectedTags = [];
    formError = null;
    formSuccess = null;
    isSubmitting = false;
  }

  async function handleCreatePost() {
    // Clear previous messages
    formError = null;
    formSuccess = null;

    // Validate form
    const hasTitle = postTitle.some((item) => item.value.trim().length > 0);
    const hasBody = postBody.some((item) => item.value.trim().length > 0);

    if (!hasTitle) {
      formError = t.titleRequired;
      return;
    }

    if (!hasBody) {
      formError = t.bodyRequired;
      return;
    }

    isSubmitting = true;

    try {
      const { id } = await api.reactor.post.post({
        hubId: hubId,
        communityId: communityId,
        title: postTitle.filter((item) => item.value.trim().length > 0),
        body: postBody.filter((item) => item.value.trim().length > 0),
        tagIds: selectedTags,
      });

      formSuccess = t.createSuccess;

      // Call the callback if provided
      if (onPostCreated) {
        onPostCreated();
      }

      setTimeout(() => {
        goto(toLocaleHref(`/reactor/${id}`));
      }, 1500);
    } catch (error) {
      console.error("Error creating post:", error);
      formError = error instanceof Error ? error.message : t.createError;
    } finally {
      isSubmitting = false;
    }
  }
</script>

<Modal
  {show}
  title={t.createPostTitle}
  {onClose}
  onSubmit={handleCreatePost}
  submitText={t.create}
  cancelText={t.cancel}
  submitDisabled={isSubmitting ||
    !postTitle.some((item) => item.value.trim().length > 0) ||
    !postBody.some((item) => item.value.trim().length > 0)}
  {isSubmitting}
>
  <form>
    <!-- Hub Picker -->
    {#if communityId}
      <div class="form-text text-muted">{t.hubDisabledByCommunity}</div>
    {:else}
      <div class="mb-3">
        <ReactorHubPicker
          bind:selectedHubId={hubId}
          {locale}
          label={t.hub}
          placeholder={t.hubPlaceholder}
        />
      </div>
    {/if}

    <!-- Community Picker -->
    {#if hubId}
      <div class="form-text text-muted">{t.communityDisabledByHub}</div>
    {:else}
      <div class="mb-3">
        <ReactorCommunityPicker
          bind:selectedCommunityId={communityId}
          {hubId}
          {locale}
          label={t.community}
          placeholder={t.communityPlaceholder}
        />
      </div>
    {/if}

    <!-- Title Input -->
    <LocalizedInput
      {locale}
      id="post-title"
      label={t.title}
      placeholder={t.titlePlaceholder}
      required
      bind:value={postTitle}
    />

    <!-- Body Editor -->
    <LocalizedEditor {locale} id="post-body" label={t.body} required bind:value={postBody} />

    <!-- Tag Picker -->
    <TagPicker {locale} label={t.tags} bind:selectedTagIds={selectedTags} />

    <!-- Error Message -->
    {#if formError}
      <div class="alert alert-danger mt-3" role="alert">
        {formError}
      </div>
    {/if}

    <!-- Success Message -->
    {#if formSuccess}
      <div class="alert alert-success mt-3" role="alert">
        {formSuccess}
      </div>
    {/if}
  </form>
</Modal>
