<script lang="ts">
  import type { Common } from "@commune/api";

  interface Props {
    locale: Common.WebsiteLocale;
    id: string;
    label: string;
    placeholder: string;
    required?: boolean;
    value: Common.Localizations;
  }

  const i18n = {
    en: {
      languages: {
        en: "English",
        ru: "Russian",
      },
      providedTranslations: "Provided translations:",
    },
    ru: {
      languages: {
        en: "Английский",
        ru: "Русский",
      },
      providedTranslations: "Указанные переводы:",
    },
  };

  let { value = $bindable(), ...props }: Props = $props();

  const { id, label, placeholder, required = false, locale } = props;

  const t = $derived(i18n[locale]);

  let selectedLanguage = $state<Common.WebsiteLocale>("en");

  // Find the value for the currently selected language
  function getCurrentValue() {
    const localization = value.find((val) => val.locale === selectedLanguage);
    return localization?.value || "";
  }

  // Update the value for the currently selected language
  function handleChange(newValue: string) {
    if (newValue.length) {
      const updatedValues = [...value];
      const existingIndex = updatedValues.findIndex((val) => val.locale === selectedLanguage);

      if (existingIndex >= 0) {
        updatedValues[existingIndex] = { locale: selectedLanguage, value: newValue };
      } else {
        updatedValues.push({ locale: selectedLanguage, value: newValue });
      }

      value = updatedValues;
    } else {
      value = value.filter((val) => val.locale !== selectedLanguage);
    }
  }

  // Switch the selected language
  function handleLanguageChange(langCode: Common.WebsiteLocale) {
    selectedLanguage = langCode;
  }

  // Get the display name of the current language
  function getLanguageDisplay() {
    return selectedLanguage.toUpperCase();
  }
</script>

<div class="mb-3">
  <label for={id} class="form-label">
    {label}
    {#if required}
      <span class="text-danger">*</span>
    {/if}
  </label>
  <div class="input-group">
    <div class="dropdown">
      <button
        class="btn btn-outline-secondary dropdown-toggle"
        type="button"
        id={`dropdown-${id}`}
        data-bs-toggle="dropdown"
        aria-expanded="false"
        style="width: 60px; display: flex; justify-content: space-between; align-items: center;"
      >
        {getLanguageDisplay()}
      </button>
      <ul class="dropdown-menu" aria-labelledby={`dropdown-${id}`}>
        <li>
          <button
            class="dropdown-item {selectedLanguage === 'en' ? 'active' : ''}"
            type="button"
            onclick={() => handleLanguageChange("en")}
          >
            {t.languages.en}
          </button>
        </li>
        <li>
          <button
            class="dropdown-item {selectedLanguage === 'ru' ? 'active' : ''}"
            type="button"
            onclick={() => handleLanguageChange("ru")}
          >
            {t.languages.ru}
          </button>
        </li>
      </ul>
    </div>
    <input
      type="text"
      class="form-control"
      {id}
      {placeholder}
      value={getCurrentValue()}
      oninput={(e) => handleChange(e.currentTarget.value)}
      {required}
    />
  </div>

  {#if value.length > 0}
    <div class="mt-2 small text-muted">
      <div>{t.providedTranslations}</div>
      <ul class="list-unstyled mb-0 mt-1">
        {#each value.filter(Boolean) as val (val.locale)}
          <li class="badge bg-light text-dark me-1">
            {t.languages[val.locale]}: {val.value}
          </li>
        {/each}
      </ul>
    </div>
  {/if}
</div>
