<script lang="ts">
  import type { Common, Reactor } from "@commune/api";
  import type { GetAppropriateLocalization } from "$lib";

  import { getClient } from "$lib/acrpc";

  interface Props {
    post: Reactor.GetPostsOutput[number];
    locale: Common.WebsiteLocale;
    toLocaleHref: (href: string) => string;
    getAppropriateLocalization: GetAppropriateLocalization;
  }

  const { post, locale, toLocaleHref, getAppropriateLocalization }: Props = $props();

  const i18n = {
    en: {
      usefulness: "Usefulness",
      getPlural(n: number) {
        if (n === 1) return 0;

        return 1;
      },
      ratingTooltipText(rating: Reactor.Rating) {
        const likesWord = ["like", "likes"][this.getPlural(rating.likes % 10)];
        const dislikesWord = ["dislike", "dislikes"][this.getPlural(rating.dislikes % 10)];

        return `${rating.likes} ${likesWord}, ${rating.dislikes} ${dislikesWord}`;
      },
      time: {
        days(n: number) {
          return `${n} ${n === 1 ? "day" : "days"} ago`;
        },
        hours(n: number) {
          return `${n} ${n === 1 ? "hour" : "hours"} ago`;
        },
        minutes(n: number) {
          return `${n} ${n === 1 ? "minute" : "minutes"} ago`;
        },
        seconds(n: number) {
          return `${n} ${n === 1 ? "second" : "seconds"} ago`;
        },
        rightNow: "right now",
      },
    },
    ru: {
      usefulness: "Полезность",
      getPlural(n: number) {
        if (n === 1) return 0;
        if (n >= 2 && n <= 4) return 1;

        return 2;
      },
      ratingTooltipText(rating: Reactor.Rating) {
        const likesWord = ["лайк", "лайка", "лайков"][this.getPlural(rating.likes % 10)];
        const dislikesWord = ["дизлайк", "дизлайка", "дизлайков"][
          this.getPlural(rating.dislikes % 10)
        ];

        return `${rating.likes} ${likesWord}, ${rating.dislikes} ${dislikesWord}`;
      },
      time: {
        days(n: number) {
          const word = ["день", "дня", "дней"][i18n.ru.getPlural(n)];

          return `${n} ${word} назад`;
        },

        hours(n: number) {
          const word = ["час", "часа", "часов"][i18n.ru.getPlural(n)];

          return `${n} ${word} назад`;
        },

        minutes(n: number) {
          const word = ["минуту", "минуты", "минут"][i18n.ru.getPlural(n)];

          return `${n} ${word} назад`;
        },

        seconds(n: number) {
          const word = ["секунду", "секунды", "секунд"][i18n.ru.getPlural(n)];

          return `${n} ${word} назад`;
        },

        rightNow: "только что",
      },
    },
  };

  const { fetcher: api } = getClient();

  const t = $derived(i18n[locale]);

  let rating = $state(post.rating);
  let usefulness = $state(post.usefulness);

  const ratingValue = $derived(rating.likes - rating.dislikes);
  const usefulnessValue = $derived(usefulness.totalValue ?? 0);

  const ratingTooltipText = $derived(t.ratingTooltipText(rating));

  // Hover state for usefulness stars
  let hoveredStarIndex = $state<number | null>(null);

  const authorName = $derived(getAppropriateLocalization(post?.author?.name ?? []));
  const title = $derived(getAppropriateLocalization(post?.title ?? []));
  const body = $derived(getAppropriateLocalization(post?.body ?? []));
  const hubName = $derived(post?.hub ? getAppropriateLocalization(post.hub.name ?? []) : null);
  const communityName = $derived(
    post?.community ? getAppropriateLocalization(post.community.name ?? []) : null,
  );

  let copied = $state(false);
  let copiedTagId = $state<string | null>(null);

  // Format date for display
  function formatDate(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffDay > 0) {
      return t.time.days(diffDay);
    } else if (diffHour > 0) {
      return t.time.hours(diffHour);
    } else if (diffMin > 0) {
      return t.time.minutes(diffMin);
    } else if (diffSec > 3) {
      return t.time.seconds(diffSec);
    } else {
      return t.time.rightNow;
    }
  }

  function copyLink(evt: MouseEvent) {
    if (!evt.ctrlKey && !evt.shiftKey && !evt.altKey && !evt.metaKey) {
      evt.preventDefault();

      const link = `${window.location.origin}${toLocaleHref(`/reactor/${post.id}`)}`;
      navigator.clipboard.writeText(link);

      copied = true;

      setTimeout(() => (copied = false), 2000);
    }
  }

  async function like() {
    rating = await api.reactor.post.rating.post({
      id: post.id,
      type: "like",
    });
  }

  async function dislike() {
    rating = await api.reactor.post.rating.post({
      id: post.id,
      type: "dislike",
    });
  }

  async function rateUsefulness(value: number) {
    usefulness = await api.reactor.post.usefulness.post({
      id: post.id,
      value: value === usefulness?.value ? null : value,
    });
  }

  function copyTagId(tagId: string) {
    navigator.clipboard.writeText(tagId);

    copiedTagId = tagId;

    setTimeout(() => {
      if (copiedTagId === tagId) {
        copiedTagId = null;
      }
    }, 2000);
  }
</script>

<div class="post-card mb-4">
  <div class="card">
    <div class="card-header">
      <!-- Hub and Community Header -->
      {#if post.hub || post.community}
        <div class="hub-community-header mb-3">
          <!-- Hub Row -->
          {#if post.hub}
            <div class="hub-row d-flex align-items-center mb-2">
              <div class="hub-image-container me-2">
                {#if post.hub.image}
                  <img src={`/images/${post.hub.image}`} alt={hubName || "Hub"} class="hub-image" />
                {:else}
                  <div class="hub-image-placeholder">
                    <i class="bi bi-collection text-muted"></i>
                  </div>
                {/if}
              </div>
              <a
                href={toLocaleHref(`/reactor/hubs/${post.hub.id}`)}
                class="hub-link text-decoration-none fw-medium"
              >
                {hubName}
              </a>
            </div>
          {/if}

          <!-- Community Row -->
          {#if post.community}
            <div class="community-row d-flex align-items-center">
              <div class="community-image-container me-2">
                {#if post.community.image}
                  <img
                    src={`/images/${post.community.image}`}
                    alt={communityName || "Community"}
                    class="community-image"
                  />
                {:else}
                  <div class="community-image-placeholder">
                    <i class="bi bi-people text-muted"></i>
                  </div>
                {/if}
              </div>
              <a
                href={toLocaleHref(`/reactor/communities/${post.community.id}`)}
                class="community-link text-decoration-none fw-medium"
              >
                {communityName}
              </a>
            </div>
          {/if}
        </div>
      {/if}

      <!-- Post Header with Author and Time -->
      <div class="post-header d-flex justify-content-between align-items-center mb-3">
        <div class="d-flex align-items-center">
          <!-- Rating -->
          <div class="rating-block d-flex align-items-center me-3">
            {#if ratingValue > 0}
              <span
                class="rating-value me-2 text-success"
                data-bs-toggle="tooltip"
                data-bs-placement="top"
                title={ratingTooltipText}>{ratingValue}</span
              >
            {:else if ratingValue < 0}
              <span
                class="rating-value me-2 text-danger"
                data-bs-toggle="tooltip"
                data-bs-placement="top"
                title={ratingTooltipText}>{ratingValue}</span
              >
            {:else}
              <span
                class="rating-value me-2"
                data-bs-toggle="tooltip"
                data-bs-placement="top"
                title={ratingTooltipText}>0</span
              >
            {/if}
            <div class="rating-buttons">
              <button
                class={`btn btn-sm me-1 ${rating?.status === "like" ? "btn-success" : "btn-outline-success"}`}
                aria-label="Like"
                onclick={like}
              >
                <i class="bi bi-hand-thumbs-up"></i>
              </button>
              <button
                class={`btn btn-sm ${rating?.status === "dislike" ? "btn-danger" : "btn-outline-danger"}`}
                aria-label="Dislike"
                onclick={dislike}
              >
                <i class="bi bi-hand-thumbs-down"></i>
              </button>
            </div>
          </div>

          <!-- Author info -->
          <div class="author-info d-flex align-items-center">
            {#if post.author.image}
              <img
                src={`/images/${post.author.image}`}
                alt={"avatar"}
                class="avatar rounded-circle me-2"
                width="32"
                height="32"
                style:object-fit="cover"
              />
            {:else}
              <div class="avatar rounded-circle me-2"></div>
            {/if}
            <div>
              <div class="author-name fw-bold">
                {authorName ?? "Anonymous"}
              </div>
              <div class="post-time small text-muted" title={post.createdAt.toISOString()}>
                {formatDate(post.createdAt)}
              </div>
            </div>
          </div>
        </div>

        <div class="usefulness-block">
          <div class="d-flex flex-column align-items-start">
            {#if usefulness && usefulness.count > 0}
              <span class="usefulness-label mb-1 text-muted small px-1">
                {t.usefulness} ({usefulness.count})
              </span>
            {:else}
              <span class="usefulness-label mb-1 text-muted small px-1">
                {t.usefulness}
              </span>
            {/if}

            <div
              role="group"
              aria-label="Usefulness rating"
              onmouseleave={() => (hoveredStarIndex = null)}
            >
              {#each Array(5) as _, i}
                <button
                  class="btn btn-sm p-0 px-1"
                  aria-label={`Rate usefulness ${i + 1}`}
                  onclick={() => rateUsefulness((i + 1) * 2)}
                  onmouseenter={() => (hoveredStarIndex = i)}
                >
                  <i
                    class="bi bi-star{(
                      hoveredStarIndex !== null
                        ? i <= hoveredStarIndex
                        : (i + 1) * 2 <= usefulnessValue
                    )
                      ? '-fill'
                      : ''} text-warning rating-star"
                  ></i>
                </button>
              {/each}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="card-body">
      <a href={toLocaleHref(`/reactor/${post.id}`)} class="post-title-link-wrapper">
        <h5 class="card-title">{title}</h5>
      </a>

      <div class="card-text">{@html body}</div>

      <div class="tags mb-3">
        {#each [...post.tags] as tag}
          <button
            class={`badge me-1 ${copiedTagId === tag.id ? "bg-success text-white" : "bg-light text-secondary"}`}
            onclick={copyTagId.bind(null, tag.id)}
          >
            {getAppropriateLocalization(tag.name)}
          </button>
        {/each}
      </div>

      <div class="card-actions d-flex">
        <!-- <button class="btn btn-sm btn-outline-secondary me-2" aria-label="Save">
          <i class="bi bi-bookmark"></i>
        </button> -->
        <a href={toLocaleHref(`/reactor/${post.id}`)} target="_blank">
          <button
            class={`btn btn-sm ${copied ? "btn-success" : "btn-outline-secondary"}`}
            aria-label="Copy link"
            onclick={copyLink}
          >
            {#if copied}
              <i class="bi bi-check-circle"></i>
            {:else}
              <i class="bi bi-link-45deg"></i>
            {/if}
          </button>
        </a>
      </div>
    </div>
  </div>
</div>

<style>
  .post-card {
    transition: all 0.2s ease;
  }

  .rating-value {
    font-weight: bold;
    min-width: 30px;
    text-align: center;
  }

  .card-title {
    font-weight: 600;
  }

  .post-title-link-wrapper {
    display: block;
    color: black;
    text-decoration: none;
    width: 100%;
  }

  .post-title-link-wrapper:visited {
    color: gray;
  }

  .post-title-link-wrapper:visited .card-title {
    color: gray;
  }

  .post-title-link-wrapper:hover {
    color: black;
  }

  /* .post-title-link-wrapper:hover .card-title {
    color: black;
  } */

  .text-gradient-overlay {
    position: relative;
    height: 20px;
    margin-top: -20px;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
  }

  .tags {
    margin-top: 1rem;
  }

  .badge {
    font-weight: 500;
    padding: 0.5em 0.7em;
  }

  /* Hub and Community Header Styles */
  .hub-community-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.75rem;
  }

  .hub-image-container,
  .community-image-container {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    overflow: hidden;
  }

  .hub-image,
  .community-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
  }

  .hub-image-placeholder,
  .community-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
  }

  .hub-image-placeholder i,
  .community-image-placeholder i {
    font-size: 14px;
  }

  .hub-link,
  .community-link {
    color: #495057;
    font-size: 0.9rem;
  }

  .hub-link:hover,
  .community-link:hover {
    color: #007bff;
    text-decoration: underline !important;
  }
</style>
