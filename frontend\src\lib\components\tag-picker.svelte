<script lang="ts">
  import type { Tag, Common } from "@commune/api";

  import { getClient } from "$lib/acrpc";

  type TagEntity = Tag.GetTagsOutput[number];

  interface Props {
    locale: Common.WebsiteLocale;
    selectedTagIds: string[];
    label?: string;
    placeholder?: string;
  }

  const i18n = {
    en: {
      tags: "Tags",
      addTag: "Add tag",
      searchTags: "Search tags...",
      noTagsFound: "No tags found",
      loading: "Loading...",
    },
    ru: {
      tags: "Теги",
      addTag: "Добавить тег",
      searchTags: "Поиск тегов...",
      noTagsFound: "Теги не найдены",
      loading: "Загрузка...",
    },
  };

  const { fetcher: api } = getClient();

  let { selectedTagIds = $bindable(), locale, label, placeholder }: Props = $props();

  const t = $derived(i18n[locale]);

  // State
  let showTagInput = $state(false);
  let tagSearchQuery = $state("");
  let searchResults = $state<Tag.GetTagsOutput>([]);
  let selectedTags = $state<Tag.GetTagsOutput>([]);
  let isSearching = $state(false);
  let searchDebounceTimeout = $state<ReturnType<typeof setTimeout> | null>(null);

  // Helper function to get appropriate localization
  function getAppropriateLocalization(localizations: Common.Localizations): string {
    const localization = localizations.find((l) => l.locale === locale);
    return localization?.value || localizations[0]?.value || "";
  }

  // Load selected tags on mount and when selectedTagIds change
  $effect(() => {
    if (selectedTagIds.length > 0) {
      loadSelectedTags();
    } else {
      selectedTags = [];
    }
  });

  async function loadSelectedTags() {
    try {
      selectedTags = await api.tag.list.get({ ids: selectedTagIds });
    } catch (error) {
      console.error("Failed to load selected tags:", error);
    }
  }

  async function searchTags(query: string) {
    const searchQuery = query.trim();

    if (!searchQuery) {
      searchResults = [];
      return;
    }

    isSearching = true;

    try {
      searchResults = await api.tag.list
        .get({ query: searchQuery })
        .then((r) => r.filter((tag) => !selectedTagIds.includes(tag.id)));
    } catch (error) {
      console.error("Failed to search tags:", error);
      searchResults = [];
    } finally {
      isSearching = false;
    }
  }

  function debounceSearch(query: string) {
    if (searchDebounceTimeout) {
      clearTimeout(searchDebounceTimeout);
    }

    searchDebounceTimeout = setTimeout(() => {
      searchTags(query);
    }, 300);
  }

  function handleSearchInput(event: Event) {
    const target = event.target as HTMLInputElement;
    tagSearchQuery = target.value;
    debounceSearch(tagSearchQuery);
  }

  function addTag(tag: TagEntity) {
    console.dir(
      {
        tag,
        selectedTagIds,
        selectedTags,
        searchResults,
      },
      { depth: null },
    );

    if (selectedTagIds.includes(tag.id)) {
      return;
    }

    selectedTagIds = [...selectedTagIds, tag.id];
    selectedTags = [...selectedTags, tag];

    // Remove from search results
    searchResults = searchResults.filter((t) => t.id !== tag.id);

    // Clear search
    tagSearchQuery = "";
    searchResults = [];
    showTagInput = false;
  }

  function removeTag(tagId: string) {
    selectedTagIds = selectedTagIds.filter((id) => id !== tagId);
    selectedTags = selectedTags.filter((tag) => tag.id !== tagId);
  }

  function openTagInput() {
    showTagInput = true;
    tagSearchQuery = "";
    searchResults = [];
  }

  function closeTagInput() {
    showTagInput = false;
    tagSearchQuery = "";
    searchResults = [];
  }
</script>

<div class="mb-3">
  {#if label && showTagInput}
    <label for="tag-search-input" class="form-label">{label}</label>
  {/if}

  <div class="tag-picker">
    <!-- Selected Tags -->
    <div class="selected-tags d-flex flex-wrap gap-2 mb-2">
      {#each selectedTags as tag (tag.id)}
        <span class="badge bg-primary d-flex align-items-center">
          {getAppropriateLocalization(tag.name)}
          <button
            type="button"
            class="btn-close btn-close-white ms-1"
            style="font-size: 0.7em;"
            onclick={() => removeTag(tag.id)}
            aria-label="Remove tag"
          ></button>
        </span>
      {/each}

      <!-- Add Tag Button -->
      {#if !showTagInput}
        <button type="button" class="btn btn-outline-secondary btn-sm" onclick={openTagInput}>
          <i class="bi bi-plus"></i>
          {t.addTag}
        </button>
      {/if}
    </div>

    <!-- Tag Search Input -->
    {#if showTagInput}
      <div class="tag-search-container position-relative">
        <div class="input-group">
          <input
            type="text"
            id="tag-search-input"
            class="form-control"
            placeholder={placeholder || t.searchTags}
            bind:value={tagSearchQuery}
            oninput={handleSearchInput}
          />
          <button
            type="button"
            class="btn btn-outline-secondary"
            onclick={closeTagInput}
            aria-label="Close tag search"
          >
            <i class="bi bi-x"></i>
          </button>
        </div>

        <!-- Search Results Dropdown -->
        {#if tagSearchQuery.trim() && (isSearching || searchResults.length > 0)}
          <div
            class="search-results position-absolute w-100 bg-white border border-top-0 rounded-bottom shadow-sm"
            style="z-index: 1000; max-height: 200px; overflow-y: auto;"
          >
            {#if isSearching}
              <div class="p-2 text-muted">
                <i class="bi bi-hourglass-split me-1"></i>
                {t.loading}
              </div>
            {:else if searchResults.length === 0}
              <div class="p-2 text-muted">
                {t.noTagsFound}
              </div>
            {:else}
              {#each searchResults as tag (tag.id)}
                <button
                  type="button"
                  class="dropdown-item d-flex align-items-center"
                  onclick={() => addTag(tag)}
                >
                  <i class="bi bi-tag me-2"></i>
                  {getAppropriateLocalization(tag.name)}
                </button>
              {/each}
            {/if}
          </div>
        {/if}
      </div>
    {/if}
  </div>
</div>

<style>
  .tag-picker {
    position: relative;
  }

  .selected-tags {
    min-height: 2rem;
  }

  .search-results {
    border-top: none !important;
  }

  .search-results .dropdown-item {
    border: none;
    background: none;
    text-align: left;
    width: 100%;
  }

  .search-results .dropdown-item:hover {
    background-color: var(--bs-light);
  }
</style>
