// @ts-nocheck
import type {
  LayoutServerLoad,
  LayoutServerLoadEvent,
} from "./$types";

import Negotiator from "negotiator";
import { match } from "@formatjs/intl-localematcher";
import { Common } from "@commune/api";

export const load = (event: Parameters<LayoutServerLoad>[0]) => {
  const userLocales = getUserLocales(event);
  const preferredLocale = getPreferredLocale(event);

  return {
    preferredLocale,
    userLocales,
  };
};

function getUserLocales(event: LayoutServerLoadEvent) {
  const acceptLanguage = event.request.headers.get("accept-language");

  if (acceptLanguage) {
    const negotiatorRequest = {
      headers: {
        "accept-language": acceptLanguage,
      },
    };

    const preferredLanguages = new Negotiator(negotiatorRequest).languages();

    return preferredLanguages;
  }

  return [];
}

function getPreferredLocale(event: LayoutServerLoadEvent) {
  const userLocales = getUserLocales(event);

  return match(
    userLocales,
    Common.WebsiteLocaleSchema._def.values,
    "en",
  ) as Common.WebsiteLocale;
}
